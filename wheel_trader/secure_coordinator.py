"""
Enhanced Secure Coordinator for smolagents + E2B Integration

This module provides a secure coordinator that manages smolagents execution
through E2B sandboxes with health monitoring, memory coordination,
and comprehensive observability.
"""

import logging
import time
from typing import Any, Dict, List, Optional

from .agent_health import AgentHealthManager
from .evaluator import evaluate_run
from .smolagents_e2b import (
    HealthAwareAgent,
    market_analysis_tool,
    options_data_tool,
    risk_assessment_tool,
)

# Configure logging
logger = logging.getLogger(__name__)

# Import memory and observability modules
try:
    from .memory.manager import MemoryManager
    from .observability import ObservabilityManager
    from .observability.tracer import trace_execution
    MEMORY_AVAILABLE = True
except ImportError as e:
    logger.warning(f"Memory/Observability modules not available: {e}")
    MEMORY_AVAILABLE = False


class SecureCoordinator:
    """
    Enhanced coordinator with native E2B security, health monitoring,
    memory coordination, and comprehensive observability.
    Manages secure execution of smolagents through native E2B support.
    """

    def __init__(self, health_threshold: float = 0.7,
                 memory_backend: str = "pgvector",
                 memory_config: Optional[Dict] = None,
                 enable_observability: bool = True):
        """
        Initialize enhanced secure coordinator

        Args:
            health_threshold: Health threshold for agent execution gating
            memory_backend: Memory backend type ("pgvector" or "mem0")
            memory_config: Backend-specific memory configuration
            enable_observability: Whether to enable observability features
        """
        self.health_manager = AgentHealthManager(health_threshold)
        self.agents = {}
        self.available_tools = {
            "options_data": options_data_tool,
            "market_analysis": market_analysis_tool,
            "risk_assessment": risk_assessment_tool
        }

        # Initialize shared memory system if available
        self.shared_memory_manager = None
        if MEMORY_AVAILABLE:
            try:
                self.shared_memory_manager = MemoryManager(
                    backend=memory_backend,
                    config=memory_config or {},
                    agent_name="coordinator",
                    health_manager=self.health_manager
                )
                logger.info(
                    "SecureCoordinator: Shared memory system initialized"
                )
            except Exception as e:
                logger.warning(f"SecureCoordinator: Memory system failed: {e}")

        # Initialize observability system if available
        self.observability_manager = None
        if MEMORY_AVAILABLE and enable_observability:
            try:
                self.observability_manager = ObservabilityManager(
                    agent_name="coordinator",
                    memory_manager=self.shared_memory_manager
                )
                logger.info(
                    "SecureCoordinator: Observability system initialized"
                )
            except Exception as e:
                logger.warning(f"SecureCoordinator: Observability failed: {e}")

        # Coordination statistics
        self.stats = {
            "agents_created": 0,
            "executions_coordinated": 0,
            "tools_executed": 0,
            "memory_operations": 0,
            "start_time": time.time()
        }

    def register_agent(self, name: str, agent_instance: HealthAwareAgent):
        """Register an agent with the coordinator"""
        self.agents[name] = agent_instance

    def create_secure_agent(
        self,
        name: str,
        model=None,
        tools: Optional[List] = None,
        memory_backend: Optional[str] = None,
        enable_observability: Optional[bool] = None
    ) -> HealthAwareAgent:
        """
        Create a new secure agent with enhanced capabilities.

        Args:
            name: Agent name
            model: LLM model to use (defaults to InferenceClientModel)
            tools: Additional tools to include
            memory_backend: Memory backend override (uses coordinator
                default if None)
            enable_observability: Observability override (uses coordinator
                default if None)

        Returns:
            Enhanced HealthAwareAgent instance
        """
        if tools is None:
            tools = list(self.available_tools.values())
        else:
            tools.extend(self.available_tools.values())

        # Use coordinator defaults if not specified
        agent_memory_backend = memory_backend or "pgvector"
        agent_observability = (
            enable_observability
            if enable_observability is not None
            else self.observability_manager is not None
        )

        # Create enhanced agent
        agent = HealthAwareAgent(
            name=name,
            model=model,
            tools=tools,
            health_threshold=self.health_manager.health_threshold,
            memory_backend=agent_memory_backend,
            memory_config={},
            enable_observability=agent_observability
        )

        self.register_agent(name, agent)
        self.stats["agents_created"] += 1

        # Store agent creation in shared memory if available
        if self.shared_memory_manager:
            try:
                self.shared_memory_manager.store_memory(
                    content=f"Created agent '{name}' with {len(tools)} tools",
                    metadata={
                        "agent_name": name,
                        "tools_count": len(tools),
                        "memory_backend": agent_memory_backend,
                        "observability_enabled": agent_observability
                    },
                    mem_type="agent_creation",
                    importance=0.6,
                    tags=["coordination", "agent_management"]
                )
                self.stats["memory_operations"] += 1
            except Exception as e:
                logger.warning(
                    f"SecureCoordinator: Failed to store agent creation: {e}"
                )

        logger.info(f"SecureCoordinator: Created enhanced agent '{name}'")
        return agent

    # @trace_execution if MEMORY_AVAILABLE else lambda x: x
    def execute_agent(
        self, agent_name: str, task: str, **kwargs
    ) -> Dict[str, Any]:
        """
        Execute a specific agent with enhanced coordination, memory
        integration, and comprehensive observability.
        """
        if agent_name not in self.agents:
            return {
                "success": False,
                "error": f"Agent '{agent_name}' not found",
                "result": None,
                "health_check": "skipped"
            }

        # Check agent health before execution
        if not self.health_manager.is_agent_healthy(agent_name):
            health_metrics = self.health_manager.get_health_metrics(agent_name)
            health_score = (
                health_metrics.health_score if health_metrics else 0.0
            )

            return {
                "success": False,
                "error": (
                    f"Agent '{agent_name}' is unhealthy "
                    f"(score: {health_score:.2f})"
                ),
                "result": None,
                "health_check": "failed",
                "health_score": health_score
            }

        # Retrieve coordination context from shared memory if available
        coordination_context = []
        if self.shared_memory_manager:
            try:
                coordination_context = (
                    self.shared_memory_manager.search_memories(
                        query=f"agent {agent_name} {task}",
                        limit=3,
                        filters={
                            "mem_type": ["coordination", "agent_execution"]
                        }
                    )
                )
            except Exception as e:
                logger.warning(
                    f"SecureCoordinator: Memory retrieval failed: {e}"
                )

        start_time = time.time()

        # Execute with observability if available
        if self.observability_manager:
            with self.observability_manager.trace_with_memory(
                "agent_coordination"
            ):
                return self._execute_agent_with_memory(
                    agent_name,
                    task,
                    coordination_context,
                    start_time,
                    **kwargs
                )
        else:
            return self._execute_agent_basic(
                agent_name, task, start_time, **kwargs
            )

    def _execute_agent_with_memory(
        self,
        agent_name: str,
        task: str,
        context: List[Dict],
        start_time: float,
        **kwargs
    ) -> Dict[str, Any]:
        """Execute agent with memory storage of coordination results"""
        try:
            agent = self.agents[agent_name]

            # Execute the agent (enhanced agents have memory integration)
            if hasattr(agent, 'run'):
                result = agent.run(task, **kwargs)
            elif hasattr(agent, 'execute'):
                result = agent.execute(task, **kwargs)
            else:
                # Try calling the agent directly
                result = agent(task, **kwargs)

            execution_time = time.time() - start_time

            # Store coordination result in shared memory
            if self.shared_memory_manager:
                self._store_coordination_memory(
                    agent_name, task, result, execution_time, True, context
                )

            # Update statistics
            self.stats["executions_coordinated"] += 1

            # Evaluate the result
            evaluation = evaluate_run(
                agent_name=agent_name,
                agent_version="1.0",
                result={
                    "exit_code": 0,
                    "duration_ms": int(execution_time * 1000),
                    "output": str(result)
                }
            )

            # Update agent health after successful execution
            self.health_manager.update_agent_health(agent_name)

            return {
                "success": True,
                "result": result,
                "evaluation": evaluation,
                "execution_time": execution_time,
                "health_check": "passed",
                "coordination_context": len(context)
            }

        except Exception as e:
            execution_time = time.time() - start_time

            # Store error in shared memory
            if self.shared_memory_manager:
                self._store_coordination_memory(
                    agent_name, task, str(e), execution_time, False, context
                )

            # Log the error
            evaluation = evaluate_run(
                agent_name=agent_name,
                agent_version="1.0",
                result={
                    "exit_code": 1,
                    "duration_ms": int(execution_time * 1000),
                    "error": str(e)
                }
            )

            # Update agent health after failed execution
            self.health_manager.update_agent_health(agent_name)

            return {
                "success": False,
                "error": str(e),
                "result": None,
                "evaluation": evaluation,
                "execution_time": execution_time,
                "health_check": "passed"
            }

    def _execute_agent_basic(
        self,
        agent_name: str,
        task: str,
        start_time: float,
        **kwargs
    ) -> Dict[str, Any]:
        """Basic agent execution without memory storage (fallback)"""
        try:
            agent = self.agents[agent_name]

            # Execute the agent
            if hasattr(agent, 'run'):
                result = agent.run(task, **kwargs)
            elif hasattr(agent, 'execute'):
                result = agent.execute(task, **kwargs)
            else:
                result = agent(task, **kwargs)

            execution_time = time.time() - start_time
            self.stats["executions_coordinated"] += 1

            # Evaluate the result
            evaluation = evaluate_run(
                agent_name=agent_name,
                agent_version="1.0",
                result={
                    "exit_code": 0,
                    "duration_ms": int(execution_time * 1000),
                    "output": str(result)
                }
            )

            # Update agent health after successful execution
            self.health_manager.update_agent_health(agent_name)

            return {
                "success": True,
                "result": result,
                "evaluation": evaluation,
                "execution_time": execution_time,
                "health_check": "passed"
            }

        except Exception as e:
            execution_time = time.time() - start_time

            # Log the error
            evaluation = evaluate_run(
                agent_name=agent_name,
                agent_version="1.0",
                result={
                    "exit_code": 1,
                    "duration_ms": int(execution_time * 1000),
                    "error": str(e)
                }
            )

            # Update agent health after failed execution
            self.health_manager.update_agent_health(agent_name)

            return {
                "success": False,
                "error": str(e),
                "result": None,
                "evaluation": evaluation,
                "execution_time": execution_time,
                "health_check": "passed"
            }

    def _store_coordination_memory(
        self,
        agent_name: str,
        task: str,
        result: Any,
        execution_time: float,
        success: bool,
        context: List[Dict]
    ):
        """Store coordination result as memory"""
        try:
            status = "SUCCESS" if success else "FAILED"
            content = (
                f"Coordinated {agent_name}: {task} | {status} | "
                f"{execution_time:.2f}s"
            )

            if success:
                result_str = str(result)[:200] if result else "No output"
                content += f" | Result: {result_str}"
            else:
                content += f" | Error: {result}"

            metadata = {
                "agent_name": agent_name,
                "task": task,
                "execution_time": execution_time,
                "success": success,
                "coordination_context_count": len(context),
                "coordinated_by": "SecureCoordinator"
            }

            # Higher importance for coordination activities
            importance = 0.8 if success else 0.9

            self.shared_memory_manager.store_memory(
                content=content,
                metadata=metadata,
                mem_type="coordination",
                importance=importance,
                tags=["coordination", "agent_execution", agent_name]
            )

            self.stats["memory_operations"] += 1

        except Exception as e:
            logger.warning(
                f"SecureCoordinator: Failed to store coordination memory: {e}"
            )

    def execute_tool_directly(
        self, tool_name: str, **kwargs
    ) -> Dict[str, Any]:
        """
        Execute a tool directly without going through an agent.

        Args:
            tool_name: Name of the tool to execute
            **kwargs: Arguments to pass to the tool

        Returns:
            Execution result dictionary
        """
        if tool_name not in self.available_tools:
            return {
                "success": False,
                "error": f"Tool '{tool_name}' not found",
                "result": None
            }

        start_time = time.time()

        try:
            tool = self.available_tools[tool_name]
            result = tool(**kwargs)  # Direct tool call

            execution_time = time.time() - start_time

            return {
                "success": True,
                "result": result,
                "execution_time": execution_time,
                "tool": tool_name
            }

        except Exception as e:
            execution_time = time.time() - start_time

            return {
                "success": False,
                "error": str(e),
                "result": None,
                "execution_time": execution_time,
                "tool": tool_name
            }

    def get_agent_status(self) -> Dict[str, Any]:
        """Get comprehensive status including memory and observability"""
        health_data = self.health_manager.get_all_agent_health()
        unhealthy_agents = self.health_manager.get_unhealthy_agents()

        base_status = {
            "registered_agents": list(self.agents.keys()),
            "total_agents": len(self.agents),
            "available_tools": list(self.available_tools.keys()),
            "health_threshold": self.health_manager.health_threshold,
            "unhealthy_agents": unhealthy_agents,
            "health_summary": {
                name: {
                    "score": metrics.health_score,
                    "total_runs": metrics.total_runs,
                    "success_rate": metrics.success_rate
                }
                for name, metrics in health_data.items()
            },
            "coordination_stats": self.stats.copy()
        }

        # Add memory system status
        if self.shared_memory_manager:
            try:
                memory_health = self.shared_memory_manager.get_health_status()
                base_status["shared_memory"] = memory_health
            except Exception as e:
                base_status["shared_memory"] = {
                    "error": str(e),
                    "healthy": False
                }
        else:
            base_status["shared_memory"] = {"enabled": False}

        # Add observability status
        if self.observability_manager:
            try:
                obs_stats = (
                    self.observability_manager.get_observability_stats()
                )
                base_status["observability"] = obs_stats
            except Exception as e:
                base_status["observability"] = {
                    "error": str(e),
                    "healthy": False
                }
        else:
            base_status["observability"] = {"enabled": False}

        # Add individual agent enhanced status
        enhanced_agent_status = {}
        for name, agent in self.agents.items():
            try:
                if hasattr(agent, 'get_health_status'):
                    enhanced_agent_status[name] = agent.get_health_status()
                else:
                    enhanced_agent_status[name] = {"type": "basic_agent"}
            except Exception as e:
                enhanced_agent_status[name] = {"error": str(e)}

        base_status["enhanced_agent_status"] = enhanced_agent_status

        return base_status

    def refresh_agent_health(self) -> Dict[str, Any]:
        """Refresh health scores for all agents"""
        updated_count = self.health_manager.refresh_all_health_scores()

        return {
            "updated_agents": updated_count,
            "timestamp": time.time()
        }

    def search_coordination_memories(
        self, query: str, limit: int = 10
    ) -> List[Dict]:
        """Search coordination memories across all agents"""
        if not self.shared_memory_manager:
            logger.warning("SecureCoordinator: Shared memory not available")
            return []

        try:
            return self.shared_memory_manager.search_memories(
                query=query,
                limit=limit,
                filters={"mem_type": ["coordination", "agent_execution"]}
            )
        except Exception as e:
            logger.error(f"SecureCoordinator: Memory search failed: {e}")
            return []

    def get_agent_memory_summary(self, agent_name: str) -> Dict[str, Any]:
        """Get memory summary for a specific agent"""
        if agent_name not in self.agents:
            return {"error": "Agent not found"}

        agent = self.agents[agent_name]
        summary = {"agent_name": agent_name}

        # Get agent's own memory stats if available
        if hasattr(agent, 'get_memory_stats'):
            try:
                summary["agent_memory"] = agent.get_memory_stats()
            except Exception as e:
                summary["agent_memory"] = {"error": str(e)}
        else:
            summary["agent_memory"] = {"available": False}

        # Get coordination memories for this agent
        if self.shared_memory_manager:
            try:
                coordination_memories = (
                    self.shared_memory_manager.search_memories(
                        query=agent_name,
                        limit=5,
                        filters={"mem_type": ["coordination"]}
                    )
                )
                summary["coordination_memories"] = len(coordination_memories)
                summary["recent_coordinations"] = [
                    {
                        "content": mem.get("content", "")[:100],
                        "created_at": mem.get("created_at"),
                        "success": mem.get("metadata", {}).get(
                            "success", False
                        )
                    }
                    for mem in coordination_memories[:3]
                ]
            except Exception as e:
                summary["coordination_memories"] = {"error": str(e)}
        else:
            summary["coordination_memories"] = {"available": False}

        return summary

    def consolidate_all_memories(
        self, similarity_threshold: float = 0.95
    ) -> Dict[str, Any]:
        """Consolidate memories across all agents and coordination"""
        results = {"agents": {}, "shared": 0, "total": 0}

        # Consolidate shared coordination memories
        if self.shared_memory_manager:
            try:
                shared_consolidated = (
                    self.shared_memory_manager.consolidate_memories(
                        similarity_threshold=similarity_threshold
                    )
                )
                results["shared"] = shared_consolidated
                results["total"] += shared_consolidated
            except Exception as e:
                results["shared_error"] = str(e)

        # Consolidate individual agent memories
        for name, agent in self.agents.items():
            if hasattr(agent, 'consolidate_memories'):
                try:
                    agent_consolidated = agent.consolidate_memories(
                        similarity_threshold=similarity_threshold
                    )
                    results["agents"][name] = agent_consolidated
                    results["total"] += agent_consolidated
                except Exception as e:
                    results["agents"][name] = {"error": str(e)}
            else:
                results["agents"][name] = {"available": False}

        return results

    def get_coordination_stats(self) -> Dict[str, Any]:
        """Get detailed coordination statistics"""
        uptime = time.time() - self.stats["start_time"]

        return {
            **self.stats,
            "uptime_seconds": uptime,
            "avg_executions_per_minute": (
                self.stats["executions_coordinated"] / (uptime / 60)
                if uptime > 0 else 0
            ),
            "memory_enabled": self.shared_memory_manager is not None,
            "observability_enabled": self.observability_manager is not None,
            "enhanced_agents": sum(
                1 for agent in self.agents.values()
                if hasattr(agent, 'memory_manager')
            )
        }

    def get_status(self) -> Dict[str, Any]:
        """Get coordinator status - compatibility method"""
        uptime = time.time() - self.stats["start_time"]
        return {
            "status": "operational",
            "mode": "secure_coordinator",
            "available_tools": list(self.available_tools.keys()),
            "agents": list(self.agents.keys()),
            "stats": self.stats,
            "uptime_seconds": uptime,
            "health_manager_available": self.health_manager is not None,
            "memory_enabled": self.shared_memory_manager is not None,
            "observability_enabled": self.observability_manager is not None,
        }
